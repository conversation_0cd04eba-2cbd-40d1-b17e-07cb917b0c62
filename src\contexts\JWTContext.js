import PropTypes from 'prop-types';
import { createContext, useEffect, useReducer } from 'react';
import { useDispatch } from 'react-redux';
import jwtDecode from 'jwt-decode';

// reducer - state management
import { LOGIN, LOGOUT } from 'store/reducers/actions';
import authReducer from 'store/reducers/auth';
import { setUserInfoAndToken, clearUserInfoAndToken } from 'store/reducers/user';
import { openSnackbar } from 'store/reducers/snackbar';

// project-imports
import Loader from 'components/Loader';
import axios from 'axios';

// Create a dedicated axios instance for auth
const authAxios = axios.create({
  baseURL: 'https://frogdev.wigal.com.gh/api/v1',
  timeout: 50000,
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
    Accept: 'application/json'
  },
  withCredentials: true
});

// constant
const initialState = {
  isLoggedIn: false,
  isInitialized: false,
  user: null
};

const verifyToken = (serviceToken) => {
  if (!serviceToken) {
    return false;
  }
  try {
    const decoded = jwtDecode(serviceToken);
    return decoded.exp > Date.now() / 1000;
  } catch (error) {
    console.error('Token verification failed:', error);
    return false;
  }
};

const setSession = (serviceToken) => {
  if (serviceToken) {
    localStorage.setItem('serviceToken', serviceToken);
    // Set default header for the auth axios instance
    authAxios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
  } else {
    localStorage.removeItem('serviceToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('userInfo');
    delete authAxios.defaults.headers.common.Authorization;
  }
};

// Token refresh function
const refreshAccessToken = async () => {
  try {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await authAxios.post('/auth/refresh', {
      refreshToken: refreshToken
    });

    const { data } = response.data;
    const { accessToken } = data;
    const newServiceToken = accessToken.token;

    // Update stored token
    setSession(newServiceToken);

    return newServiceToken;
  } catch (error) {
    console.error('Token refresh failed:', error);
    throw error;
  }
};

// ==============================|| JWT CONTEXT & PROVIDER ||============================== //
const JWTContext = createContext(null);

export const JWTProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const reduxDispatch = useDispatch();

  // Setup axios interceptors
  useEffect(() => {
    // Request interceptor
    const requestInterceptor = authAxios.interceptors.request.use(
      (config) => {
        config.headers['Content-Type'] = 'application/json';
        config.headers['Accept'] = 'application/json';
        const token = localStorage.getItem('serviceToken');
        if (token && verifyToken(token)) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    const responseInterceptor = authAxios.interceptors.response.use(
      (response) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const newToken = await refreshAccessToken();

            // Update Redux store with new token
            const userInfo = JSON.parse(localStorage.getItem('userInfo') || 'null');
            const refreshToken = localStorage.getItem('refreshToken');

            reduxDispatch(
              setUserInfoAndToken({
                userToken: newToken,
                refreshToken: refreshToken,
                userInfo: userInfo,
                firstTimeLogin: false
              })
            );

            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return authAxios(originalRequest);
          } catch (refreshError) {
            // Refresh failed, logout user
            handleLogout();
            return Promise.reject(refreshError);
          }
        }

        // Show error message
        const errMsg = error.response?.data?.message || error.message || 'Request failed';
        reduxDispatch(
          openSnackbar({
            open: true,
            message: errMsg,
            variant: 'alert',
            alert: {
              color: 'error'
            },
            close: false
          })
        );

        return Promise.reject(error);
      }
    );

    // Cleanup interceptors
    return () => {
      authAxios.interceptors.request.eject(requestInterceptor);
      authAxios.interceptors.response.eject(responseInterceptor);
    };
  }, [reduxDispatch]);

  const handleLogout = () => {
    setSession(null);
    dispatch({ type: LOGOUT });
    reduxDispatch(clearUserInfoAndToken());
  };

  useEffect(() => {
    const init = async () => {
      try {
        const serviceToken = localStorage.getItem('serviceToken');
        const refreshToken = localStorage.getItem('refreshToken');
        const storedUserInfo = localStorage.getItem('userInfo');

        if (serviceToken && verifyToken(serviceToken)) {
          setSession(serviceToken);

          // Parse stored user info
          let user = null;
          try {
            user = storedUserInfo ? JSON.parse(storedUserInfo) : null;
          } catch (parseError) {
            console.error('Failed to parse stored user info:', parseError);
            localStorage.removeItem('userInfo');
          }

          dispatch({
            type: LOGIN,
            payload: {
              isLoggedIn: true,
              user
            }
          });

          // Update Redux store with stored data
          reduxDispatch(
            setUserInfoAndToken({
              userToken: serviceToken,
              refreshToken: refreshToken,
              userInfo: user,
              firstTimeLogin: false
            })
          );
        } else if (refreshToken) {
          // Try to refresh the token
          try {
            const newToken = await refreshAccessToken();
            const user = storedUserInfo ? JSON.parse(storedUserInfo) : null;

            dispatch({
              type: LOGIN,
              payload: {
                isLoggedIn: true,
                user
              }
            });

            reduxDispatch(
              setUserInfoAndToken({
                userToken: newToken,
                refreshToken: refreshToken,
                userInfo: user,
                firstTimeLogin: false
              })
            );
          } catch (refreshError) {
            // Refresh failed, clear everything
            handleLogout();
          }
        } else {
          // No valid tokens, logout
          handleLogout();
        }
      } catch (err) {
        console.error('Initialization error:', err);
        handleLogout();
      } finally {
        // Always set initialized to true
        dispatch({ type: 'SET_INITIALIZED' }); // You may need to add this action
      }
    };

    init();
  }, [reduxDispatch]);

  const login = async (email, password) => {
    try {
      console.log({
        email,
        password
      });
      const response = await authAxios.post('/auth/login', { email, password });

      // Extract data from the API response structure
      const { data } = response.data;
      const { accessToken, refreshToken, user, firstTimeLogin } = data;
      const serviceToken = accessToken.token;

      // Store tokens and user info
      setSession(serviceToken);
      localStorage.setItem('refreshToken', refreshToken);
      localStorage.setItem('userInfo', JSON.stringify(user));

      // Update auth context
      dispatch({
        type: LOGIN,
        payload: {
          isLoggedIn: true,
          user
        }
      });

      // Update Redux store
      reduxDispatch(
        setUserInfoAndToken({
          userToken: serviceToken,
          refreshToken: refreshToken,
          userInfo: user,
          firstTimeLogin: firstTimeLogin
        })
      );

      return { success: true, firstTimeLogin };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const logout = () => {
    handleLogout();
  };

  const resetPassword = async (email) => {
    try {
      const response = await authAxios.post('/auth/reset-password', { email });
      return response.data;
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  };

  if (state.isInitialized !== undefined && !state.isInitialized) {
    return <Loader />;
  }

  return (
    <JWTContext.Provider
      value={{
        ...state,
        login,
        logout,
        resetPassword
      }}
    >
      {children}
    </JWTContext.Provider>
  );
};

JWTProvider.propTypes = {
  children: PropTypes.node
};

export default JWTContext;

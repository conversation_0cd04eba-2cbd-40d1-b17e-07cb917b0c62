import React from 'react';
import NewWristbandModal from './modals/NewWristbandModal';
import BulkWristbandModal from './modals/BulkWristbandModal';
import AssignWristbandModal from './modals/AssignWristbandModal';
import DeleteWristbandModal from './modals/DeleteWristbandModal';

const WristbandModals = ({
  state,
  actions,
  onFormChange
}) => {
  const handleNewWristbandFormChange = (updates) => {
    onFormChange('newWristband', updates);
  };

  const handleAssignFormChange = (updates) => {
    onFormChange('assignData', updates);
  };

  const handleFileChange = (file) => {
    onFormChange('selectedFile', file);
  };

  return (
    <>
      {/* Register New Wristband Modal */}
      <NewWristbandModal
        open={state.modals.newWristband}
        onClose={actions.handleCloseNewWristband}
        formData={state.forms.newWristband}
        onFormChange={handleNewWristbandFormChange}
        onSubmit={actions.handleRegisterWristband}
      />

      {/* Register Bulk Wristbands Modal */}
      <BulkWristbandModal
        open={state.modals.bulkWristband}
        onClose={actions.handleCloseBulkWristband}
        selectedFile={state.forms.selectedFile}
        onFileChange={handleFileChange}
        onSubmit={actions.handleBulkRegister}
      />

      {/* Assign Wristbands Modal */}
      <AssignWristbandModal
        open={state.modals.assignWristband}
        onClose={actions.handleCloseAssignWristband}
        formData={state.forms.assignData}
        onFormChange={handleAssignFormChange}
        onSubmit={actions.handleAssignWristbands}
      />

      {/* Delete Wristband Modal */}
      <DeleteWristbandModal
        open={state.modals.delete}
        onClose={actions.handleCloseDelete}
        onConfirm={actions.handleDeleteWristband}
      />
    </>
  );
};

export default WristbandModals;

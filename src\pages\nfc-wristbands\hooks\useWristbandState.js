import { useState, useEffect } from 'react';
import { initialWristbands, initialNewWristband, initialAssignData } from '../constants/wristbandConstants';

export const useWristbandState = () => {
  // Consolidated state object
  const [state, setState] = useState({
    // Pagination and display
    page: 0,
    rowsPerPage: 10,
    tabValue: 0,
    filtersExpanded: true,
    
    // Search and filters
    searchTerm: '',
    dateRange: [null, null],
    school: 'All',
    status: 'All',
    tableSearchTerm: '',
    
    // Data
    wristbands: initialWristbands,
    filteredWristbands: initialWristbands,
    
    // Modal states
    modals: {
      newWristband: false,
      bulkWristband: false,
      assignWristband: false,
      delete: false
    },
    
    // Form data
    forms: {
      newWristband: initialNewWristband,
      assignData: initialAssignData,
      selectedFile: null
    }
  });

  // Update state helper
  const updateState = (updates) => {
    setState(prev => ({
      ...prev,
      ...updates
    }));
  };

  // Update nested state helper
  const updateNestedState = (key, updates) => {
    setState(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        ...updates
      }
    }));
  };

  // Modal handlers
  const toggleModal = (modalName, isOpen = null) => {
    updateNestedState('modals', {
      [modalName]: isOpen !== null ? isOpen : !state.modals[modalName]
    });
  };

  // Form handlers
  const updateForm = (formName, updates) => {
    updateNestedState('forms', {
      [formName]: {
        ...state.forms[formName],
        ...updates
      }
    });
  };

  const resetForm = (formName) => {
    const initialValues = {
      newWristband: initialNewWristband,
      assignData: initialAssignData,
      selectedFile: null
    };
    
    updateNestedState('forms', {
      [formName]: initialValues[formName]
    });
  };

  // Computed values
  const assignedCount = state.wristbands.filter(w => w.status === 'Assigned').length;
  const unassignedCount = state.wristbands.filter(w => w.status === 'Unassigned').length;

  // Filter effect
  useEffect(() => {
    let filtered = [...state.wristbands];

    // Filter by tab value
    if (state.tabValue === 1) {
      filtered = filtered.filter(w => w.status === 'Assigned');
    } else if (state.tabValue === 2) {
      filtered = filtered.filter(w => w.status === 'Unassigned');
    }

    // Filter by school
    if (state.school !== 'All') {
      filtered = filtered.filter(w => w.assignedSchool === state.school);
    }

    // Filter by status
    if (state.status !== 'All') {
      filtered = filtered.filter(w => w.status === state.status);
    }

    // Filter by date range
    if (state.dateRange[0] && state.dateRange[1]) {
      const startDate = new Date(state.dateRange[0]);
      const endDate = new Date(state.dateRange[1]);

      filtered = filtered.filter(w => {
        if (w.dateRegistered === 'N/A') return false;
        const parts = w.dateRegistered.split('/');
        const wristbandDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
        return wristbandDate >= startDate && wristbandDate <= endDate;
      });
    }

    // Filter by global search
    if (state.searchTerm) {
      const term = state.searchTerm.toLowerCase();
      filtered = filtered.filter(w =>
        w.modelName.toLowerCase().includes(term) ||
        w.modelNumber.toLowerCase().includes(term) ||
        w.serialNumber.toLowerCase().includes(term) ||
        w.assignedSchool.toLowerCase().includes(term) ||
        w.status.toLowerCase().includes(term)
      );
    }

    // Filter by table search
    if (state.tableSearchTerm) {
      const term = state.tableSearchTerm.toLowerCase();
      filtered = filtered.filter(w =>
        w.modelName.toLowerCase().includes(term) ||
        w.modelNumber.toLowerCase().includes(term) ||
        w.serialNumber.toLowerCase().includes(term) ||
        w.assignedSchool.toLowerCase().includes(term) ||
        w.status.toLowerCase().includes(term)
      );
    }

    updateState({ filteredWristbands: filtered });
  }, [
    state.wristbands,
    state.tabValue,
    state.school,
    state.status,
    state.dateRange,
    state.searchTerm,
    state.tableSearchTerm
  ]);

  return {
    state,
    updateState,
    updateNestedState,
    toggleModal,
    updateForm,
    resetForm,
    assignedCount,
    unassignedCount
  };
};

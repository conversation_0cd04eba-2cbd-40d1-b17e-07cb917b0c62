import React from 'react';
import { Grid, Typography, TextField, MenuItem, Button } from '@mui/material';
import ReusableModal from 'components/modal/reusable';
import { schoolOptions } from '../../constants/wristbandConstants';

const AssignWristbandModal = ({
  open,
  onClose,
  formData,
  onFormChange,
  onSubmit
}) => {
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onFormChange({ [name]: value });
  };

  const renderContent = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="subtitle2" gutterBottom>
          School Name
        </Typography>
        <TextField 
          select 
          fullWidth 
          name="school" 
          value={formData.school} 
          onChange={handleInputChange}
        >
          {schoolOptions.filter(option => option.value !== 'All').map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {option.label}
            </MenuItem>
          ))}
        </TextField>
      </Grid>
      <Grid item xs={12}>
        <Typography variant="subtitle2" gutterBottom>
          Number of Wristbands
        </Typography>
        <TextField
          fullWidth
          name="count"
          type="number"
          placeholder="Enter Number of wristbands"
          value={formData.count}
          onChange={handleInputChange}
          inputProps={{ min: 1 }}
        />
      </Grid>
    </Grid>
  );

  const actions = [
    <Button key="clear" variant="outlined" color="error" onClick={onClose}>
      Clear
    </Button>,
    <Button
      key="confirm"
      variant="contained"
      color="primary"
      onClick={onSubmit}
      disabled={!formData.school || !formData.count}
    >
      Confirm & Issue
    </Button>
  ];

  return (
    <ReusableModal
      open={open}
      onClose={onClose}
      title="Assign Wristbands"
      description="Assign wristbands to schools in the TEMVO POS system."
      actions={actions}
      ariaLabelledBy="assign-wristband-modal"
    >
      {renderContent()}
    </ReusableModal>
  );
};

export default AssignWristbandModal;

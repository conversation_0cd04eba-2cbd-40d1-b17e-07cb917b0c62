import React from 'react';
import { Button } from '@mui/material';
import ReusableModal from 'components/modal/reusable';

const DeleteWristbandModal = ({
  open,
  onClose,
  onConfirm
}) => {
  const actions = [
    <Button key="cancel" variant="outlined" color="error" onClick={onClose}>
      Cancel
    </Button>,
    <Button key="delete" variant="contained" color="primary" onClick={onConfirm}>
      Yes Delete
    </Button>
  ];

  return (
    <ReusableModal
      open={open}
      onClose={onClose}
      title="Deactivate Wristband"
      description="Are you sure you want to deactivate this wristband?"
      actions={actions}
      ariaLabelledBy="deactivate-wristband-modal"
    />
  );
};

export default DeleteWristbandModal;

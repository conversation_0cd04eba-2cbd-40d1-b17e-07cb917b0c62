import React from 'react';
import { Grid, Typography, TextField, Button } from '@mui/material';
import ReusableModal from 'components/modal/reusable';

const NewWristbandModal = ({
  open,
  onClose,
  formData,
  onFormChange,
  onSubmit
}) => {
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    onFormChange({ [name]: value });
  };

  const renderContent = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="subtitle2" gutterBottom>
          Model Name
        </Typography>
        <TextField
          fullWidth
          name="modelName"
          placeholder="Enter Model Name"
          value={formData.modelName}
          onChange={handleInputChange}
        />
      </Grid>
      <Grid item xs={12}>
        <Typography variant="subtitle2" gutterBottom>
          Model Number
        </Typography>
        <TextField
          fullWidth
          name="modelNumber"
          placeholder="Enter Model Number"
          value={formData.modelNumber}
          onChange={handleInputChange}
        />
      </Grid>
      <Grid item xs={12}>
        <Typography variant="subtitle2" gutterBottom>
          Serial Number
        </Typography>
        <TextField
          fullWidth
          name="serialNumber"
          placeholder="Enter Serial Number"
          value={formData.serialNumber}
          onChange={handleInputChange}
        />
      </Grid>
    </Grid>
  );

  const actions = [
    <Button key="cancel" variant="outlined" color="error" onClick={onClose}>
      Cancel
    </Button>,
    <Button
      key="register"
      variant="contained"
      color="primary"
      onClick={onSubmit}
      disabled={!formData.modelName || !formData.modelNumber || !formData.serialNumber}
    >
      Register Wristband
    </Button>
  ];

  return (
    <ReusableModal
      open={open}
      onClose={onClose}
      title="Register New Wristband"
      description="Register a new wristband in the TEMVO POS system."
      actions={actions}
      ariaLabelledBy="register-new-wristband-modal"
    >
      {renderContent()}
    </ReusableModal>
  );
};

export default NewWristbandModal;

import { useMutation, useQuery } from '@tanstack/react-query';
import { userService } from './index';

export const useGetParents = (filters, parentId) => {
  return useQuery(['parents', filters, parentId], () => userService.getParents(filters, parentId));
};

export const useAddParent = () => {
  return useMutation((data) => userService.addParent(data));
};

export const useGetParentById = (parentId) => {
  return useQuery(['parent', parentId], () => userService.getParentById(parentId));
};

export const useDeleteParent = () => {
  return useMutation((parentId) => userService.deleteParent(parentId));
};

export const useAssignWards = (parentId) => {
  return useMutation((data) => userService.assignWards(data, parentId));
};

export const useGetWards = (parentId) => {
  return useQuery(['wards', parentId], () => userService.getWards(parentId));
};

export const useGetSchoolById = (schoolId) => {
  return useQuery(['school', schoolId], () => userService.getSchoolById(schoolId));
};

export const useGetPOS = (filters) => {
  return useQuery(['pos', filters], () => userService.getPOS(filters));
};

export const useCreatePOS = () => {
  return useMutation((data) => userService.createPOS(data));
};

export const useBulkUploadPOS = () => {
  return useMutation((data) => userService.bulkUploadPOS(data));
};

export const useAutoAssignToSchool = () => {
  return useMutation((data) => userService.autoAssignToSchool(data));
};

export const useAssignToSchool = () => {
  return useMutation((data) => userService.assignToSchool(data));
};

export const useDeactivatePOS = (posId) => {
  return useMutation((data) => userService.deactivatePOS(data, posId));
};

export const useActivatePOS = (posId) => {
  return useMutation((data) => userService.activatePOS(data, posId));
};

export const useReAssignPOS = (posId) => {
  return useMutation((data) => userService.reAssignPOS(data, posId));
};

export const useUnassignPOS = () => {
  return useMutation((data) => userService.unassignPOS(data));
};

export const useGetPOSForSchool = (schoolId) => {
  return useQuery(['pos-for-school', schoolId], () => userService.getPOSForSchool(schoolId));
};

export const useGetPOSDeviceById = (posId) => {
  return useQuery(['pos-device', posId], () => userService.getPOSDeviceById(posId));
};

export const useGetBulkUploadCredentials = (posId) => {
  return useQuery(['bulk-upload-credentials', posId], () => userService.getBulkUploadCredentials(posId));
};

export const useDeletePOSDevice = () => {
  return useMutation((posId) => userService.deletePOSDevice(posId));
};

export const useGetWristbands = (filters) => {
  return useQuery(['wristbands', filters], () => userService.getWristbands(filters));
};

export const useGetWristbandById = (wristbandId) => {
  return useQuery(['wristband', wristbandId], () => userService.getWristbandById(wristbandId));
};

export const useAssignWristbandToStudent = (wristbandId) => {
  return useMutation((data) => userService.assignWristbandToStudent(data, wristbandId));
};

export const useAssignWristbandToSchool = (wristbandId) => {
  return useMutation((data) => userService.assignWristbandToSchool(data, wristbandId));
};

export const useUnassignWristbandFromStudent = (studentId) => {
  return useMutation((data) => userService.unassignWristbandFromStudent(data, studentId));
};

export const useCreateWristband = () => {
  return useMutation((data) => userService.createWristband(data));
};

export const useBulkUploadWristbands = () => {
  return useMutation((data) => userService.bulkUploadWristbands(data));
};

export const useAutoAssignWristbandsToSchool = () => {
  return useMutation((data) => userService.autoAssignWristbandsToSchool(data));
};

export const useAssignBulkWristbandsToSchool = () => {
  return useMutation((data) => userService.assignBulkWristbandsToSchool(data));
};

export const useDeactivateWristband = (wristbandId) => {
  return useMutation((data) => userService.deactivateWristband(data, wristbandId));
};

export const useActivateWristband = (wristbandId) => {
  return useMutation((data) => userService.activateWristband(data, wristbandId));
};

export const useGetWristbandsForCurrentSchool = (schoolId) => {
  return useQuery(['wristbands-for-school', schoolId], () => userService.getWristbandsForCurrentSchool(schoolId));
};

export const useGetUnassignedWristbands = () => {
  return useQuery(['unassigned-wristbands'], () => userService.getUnassignedWristbands());
};

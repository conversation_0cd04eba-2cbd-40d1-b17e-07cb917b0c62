/* eslint-disable no-unused-vars */
import '../../assets/datestyle.css';

import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { Box, Container } from '@mui/material';

// Project imports
import { theme } from './util';
import { useWristbandState } from './hooks/useWristbandState';
import { useWristbandActions } from './hooks/useWristbandActions';
import WristbandStats from './components/WristbandStats';
import WristbandFilters from './components/WristbandFilters';
import WristbandTable from './components/WristbandTable';
import WristbandModals from './components/WristbandModals';

export default function WristbandManagement() {
  // Use consolidated state management
  const { state, updateState, updateNestedState, toggleModal, updateForm, resetForm, assignedCount, unassignedCount } = useWristbandState();

  // Use actions hook
  const actions = useWristbandActions(state, updateState, toggleModal, resetForm);

  // Form change handler
  const handleFormChange = (formName, updates) => {
    if (formName === 'selectedFile') {
      updateNestedState('forms', { selectedFile: updates });
    } else {
      updateForm(formName, updates);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ bgcolor: 'background.default', minHeight: '100vh', py: 4 }}>
        <Container maxWidth="xl">
          {/* Stats Component */}
          <WristbandStats assignedCount={assignedCount} unassignedCount={unassignedCount} />

          {/* Filters Component */}
          <WristbandFilters
            state={state}
            onToggleFilters={actions.toggleFilters}
            onSearchChange={actions.handleSearchChange}
            onDateRangeChange={actions.handleDateRangeChange}
            onSchoolChange={actions.handleSchoolChange}
            onStatusChange={actions.handleStatusChange}
          />

          {/* Table Component */}
          <WristbandTable
            state={state}
            onTabChange={actions.handleTabChange}
            onRowsPerPageChange={actions.handleChangeRowsPerPage}
            onTableSearchChange={actions.handleTableSearchChange}
            onOpenNewWristband={actions.handleOpenNewWristband}
            onOpenBulkWristband={actions.handleOpenBulkWristband}
            onOpenAssignWristband={actions.handleOpenAssignWristband}
            onDeleteWristband={actions.handleOpenDelete}
          />

          {/* Modals Component */}
          <WristbandModals state={state} actions={actions} onFormChange={handleFormChange} />
        </Container>
      </Box>
    </ThemeProvider>
  );
}

REACT_APP_VERSION = v9.1.1
GENERATE_SOURCEMAP = false


## Public URL
PUBLIC_URL = https://ableproadmin.com/react/stage
REACT_APP_BASE_NAME = /react/stage

## Backend API URL
REACT_APP_API_URL=https://mock-data-api-nextjs.vercel.app/

## Google Map Key

REACT_APP_GOOGLE_MAPS_API_KEY=AIzaSyAXv4RQK39CskcIB8fvM1Q7XCofZcLxUXw

## Firebase - Google Auth
REACT_APP_FIREBASE_API_KEY=
REACT_APP_FIREBASE_AUTH_DOMAIN=
REACT_APP_FIREBASE_PROJECT_ID=
REACT_APP_FIREBASE_STORAGE_BUCKET=
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=
REACT_APP_FIREBASE_APP_ID=
REACT_APP_FIREBASE_MEASUREMENT_ID=

## AWS
REACT_APP_AWS_POOL_ID=
REACT_APP_AWS_APP_CLIENT_ID=

## JWT
REACT_APP_JWT_SECRET_KEY=ikRgjkhi15HJiU78-OLKfjngiu=
REACT_APP_JWT_TIMEOUT=1d

## Auth0
REACT_APP_AUTH0_CLIENT_ID=
REACT_APP_AUTH0_DOMAIN=
BASE_URL=https://frogdev.wigal.com.gh/api/v1
